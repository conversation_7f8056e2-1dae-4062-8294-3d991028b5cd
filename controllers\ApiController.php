<?php

class ApiController extends BaseController {

    public function usage() {
        $this->requireApiAuth();

        $userId = $this->getApiUserId();
        $usage = Auth::getUserUsage($userId);
        $subscription = Auth::getUserSubscription($userId);

        $this->json([
            'success' => true,
            'data' => [
                'daily_usage' => $usage['daily'],
                'monthly_usage' => $usage['monthly'],
                'daily_limit' => $subscription['daily_generations'] ?? 5,
                'monthly_limit' => $subscription['monthly_generations'] ?? 50,
                'plan' => $subscription['plan_name'] ?? 'Free'
            ]
        ]);
    }

    public function generate() {
        $this->requireApiAuth();

        $userId = $this->getApiUserId();

        // Check rate limiting
        $identifier = $this->getApiKeyIdentifier();
        if (!rate_limit_check($identifier, 'api_generate', 10, 60)) { // 10 requests per minute
            $this->json([
                'success' => false,
                'error' => 'Rate limit exceeded. Please try again later.'
            ], 429);
        }

        // Check if user can generate images
        if (!Auth::canGenerateImage($userId)) {
            $this->json([
                'success' => false,
                'error' => 'Generation limit reached for today.'
            ], 403);
        }

        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->json([
                'success' => false,
                'error' => 'Invalid JSON input.'
            ], 400);
        }

        // Validate input
        $data = [
            'prompt' => sanitize($input['prompt'] ?? ''),
            'negative_prompt' => sanitize($input['negative_prompt'] ?? ''),
            'width' => (int)($input['width'] ?? 512),
            'height' => (int)($input['height'] ?? 512),
            'style' => sanitize($input['style'] ?? ''),
            'provider' => sanitize($input['provider'] ?? ''),
        ];

        $errors = $this->validate($data, [
            'prompt' => 'required|min:3|max:1000',
            'width' => 'required',
            'height' => 'required',
            'provider' => 'required'
        ]);

        if (!empty($errors)) {
            $this->json([
                'success' => false,
                'error' => 'Validation failed.',
                'details' => $errors
            ], 400);
        }

        // Validate dimensions
        if (!is_valid_image_dimensions($data['width'], $data['height'])) {
            $this->json([
                'success' => false,
                'error' => 'Invalid image dimensions.'
            ], 400);
        }

        // Check if user has API key for selected provider
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE user_id = ? AND provider = ? AND is_active = 1");
        $stmt->execute([$userId, $data['provider']]);
        $apiKeyData = $stmt->fetch();

        if (!$apiKeyData) {
            $this->json([
                'success' => false,
                'error' => 'No API key configured for selected provider.'
            ], 400);
        }

        try {
            // Create image generation record
            $stmt = $db->prepare("
                INSERT INTO generated_images (user_id, prompt, negative_prompt, width, height, style, ai_provider, file_path, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");

            $filename = 'api_' . time() . '_' . uniqid() . '.png';
            $filePath = 'storage/generated/' . $filename;

            $stmt->execute([
                $userId,
                $data['prompt'],
                $data['negative_prompt'],
                $data['width'],
                $data['height'],
                $data['style'],
                $data['provider'],
                $filePath
            ]);

            $imageId = $db->lastInsertId();

            // Record usage
            Auth::recordUsage($userId, 'api_generation');

            // Start generation process (simplified for demo)
            $this->processApiGeneration($imageId, $data, decrypt($apiKeyData['api_key']));

            $this->json([
                'success' => true,
                'data' => [
                    'image_id' => $imageId,
                    'status' => 'pending',
                    'message' => 'Image generation started.'
                ]
            ]);

        } catch (Exception $e) {
            log_error('API image generation failed: ' . $e->getMessage(), $data);
            $this->json([
                'success' => false,
                'error' => 'Failed to start image generation.'
            ], 500);
        }
    }

    public function images() {
        $this->requireApiAuth();

        $userId = $this->getApiUserId();
        $db = Database::getInstance();

        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = min(50, (int)($_GET['per_page'] ?? 20)); // Max 50 per page
        $offset = ($page - 1) * $perPage;

        $status = sanitize($_GET['status'] ?? '');

        // Build query
        $whereConditions = ['user_id = ?'];
        $params = [$userId];

        if (!empty($status)) {
            $whereConditions[] = 'status = ?';
            $params[] = $status;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Get total count
        $countQuery = "SELECT COUNT(*) FROM generated_images $whereClause";
        $stmt = $db->prepare($countQuery);
        $stmt->execute($params);
        $totalImages = $stmt->fetchColumn();

        // Get images
        $query = "
            SELECT id, prompt, negative_prompt, width, height, style, ai_provider,
                   file_path, status, error_message, generation_time, created_at
            FROM generated_images
            $whereClause
            ORDER BY created_at DESC
            LIMIT $perPage OFFSET $offset
        ";
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $images = $stmt->fetchAll();

        // Convert file paths to URLs
        foreach ($images as &$image) {
            if ($image['status'] === 'completed' && !empty($image['file_path'])) {
                $image['image_url'] = url($image['file_path']);
                $image['download_url'] = url('/download/' . $image['id']);
            }
            unset($image['file_path']); // Don't expose internal paths
        }

        $this->json([
            'success' => true,
            'data' => $images,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalImages,
                'total_pages' => ceil($totalImages / $perPage)
            ]
        ]);
    }

    public function imageStatus($id) {
        $this->requireApiAuth();

        $userId = $this->getApiUserId();
        $db = Database::getInstance();

        $stmt = $db->prepare("
            SELECT id, status, error_message, generation_time, file_path, created_at
            FROM generated_images
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$id, $userId]);
        $image = $stmt->fetch();

        if (!$image) {
            $this->json([
                'success' => false,
                'error' => 'Image not found.'
            ], 404);
        }

        $response = [
            'success' => true,
            'data' => [
                'id' => $image['id'],
                'status' => $image['status'],
                'created_at' => $image['created_at']
            ]
        ];

        if ($image['status'] === 'completed') {
            $response['data']['image_url'] = url($image['file_path']);
            $response['data']['download_url'] = url('/download/' . $image['id']);
            $response['data']['generation_time'] = $image['generation_time'];
        } elseif ($image['status'] === 'failed') {
            $response['data']['error'] = $image['error_message'];
        }

        $this->json($response);
    }

    public function testKey() {
        $this->requireApiAuth();

        $input = json_decode(file_get_contents('php://input'), true);
        $provider = sanitize($input['provider'] ?? '');
        $keyId = (int)($input['keyId'] ?? 0);

        if (empty($provider)) {
            $this->json([
                'success' => false,
                'error' => 'Provider is required.'
            ], 400);
        }

        $userId = $this->getApiUserId();
        $db = Database::getInstance();

        // Get API key
        $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE id = ? AND user_id = ? AND provider = ?");
        $stmt->execute([$keyId, $userId, $provider]);
        $apiKeyData = $stmt->fetch();

        if (!$apiKeyData) {
            $this->json([
                'success' => false,
                'error' => 'API key not found.'
            ], 404);
        }

        try {
            // Test the API key (simplified)
            $apiKey = decrypt($apiKeyData['api_key']);
            $testResult = $this->testProviderApiKey($provider, $apiKey);

            if ($testResult['success']) {
                // Update last used timestamp
                $stmt = $db->prepare("UPDATE user_ai_provider_keys SET last_used = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$keyId]);

                $this->json([
                    'success' => true,
                    'message' => 'API key is valid and working.'
                ]);
            } else {
                $this->json([
                    'success' => false,
                    'error' => 'API key test failed: ' . $testResult['error']
                ]);
            }

        } catch (Exception $e) {
            log_error('API key test failed: ' . $e->getMessage());
            $this->json([
                'success' => false,
                'error' => 'Failed to test API key.'
            ], 500);
        }
    }

    private function requireApiAuth() {
        $apiKey = $this->getApiKeyFromRequest();

        if (empty($apiKey)) {
            $this->json([
                'success' => false,
                'error' => 'API key required.'
            ], 401);
        }

        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT user_id FROM user_api_keys WHERE api_key = ? AND is_active = 1");
        $stmt->execute([$apiKey]);
        $result = $stmt->fetch();

        if (!$result) {
            $this->json([
                'success' => false,
                'error' => 'Invalid API key.'
            ], 401);
        }

        // Store user ID for later use
        $this->apiUserId = $result['user_id'];
        $this->apiKey = $apiKey;

        // Update last used timestamp
        $stmt = $db->prepare("UPDATE user_api_keys SET last_used = CURRENT_TIMESTAMP WHERE api_key = ?");
        $stmt->execute([$apiKey]);
    }

    private function getApiKeyFromRequest() {
        // Check Authorization header
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }

        // Check X-API-Key header
        if (isset($headers['X-API-Key'])) {
            return $headers['X-API-Key'];
        }

        // Check query parameter
        return $_GET['api_key'] ?? '';
    }

    private function getApiUserId() {
        return $this->apiUserId ?? null;
    }

    private function getApiKeyIdentifier() {
        return 'api_key_' . substr(md5($this->apiKey ?? ''), 0, 8);
    }

    private function processApiGeneration($imageId, $data, $apiKey) {
        // This would typically be handled by a background job queue
        // For demo purposes, we'll simulate immediate processing

        $db = Database::getInstance();

        try {
            // Update status to generating
            $stmt = $db->prepare("UPDATE generated_images SET status = 'generating' WHERE id = ?");
            $stmt->execute([$imageId]);

            // Call AI provider API
            $result = $this->callAIProvider($data['provider'], $apiKey, $data);

            if ($result['success']) {
                // Download and save image
                $imageData = file_get_contents($result['image_url']);

                // Get file path
                $stmt = $db->prepare("SELECT file_path FROM generated_images WHERE id = ?");
                $stmt->execute([$imageId]);
                $filePath = $stmt->fetchColumn();

                // Save image
                file_put_contents($filePath, $imageData);

                // Update record
                $stmt = $db->prepare("
                    UPDATE generated_images
                    SET status = 'completed', file_size = ?, generation_time = ?
                    WHERE id = ?
                ");
                $stmt->execute([strlen($imageData), $result['generation_time'], $imageId]);
            } else {
                // Update with error
                $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
                $stmt->execute([$result['error'], $imageId]);
            }

        } catch (Exception $e) {
            $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
            $stmt->execute([$e->getMessage(), $imageId]);
        }
    }

    private function callAIProvider($provider, $apiKey, $data) {
        switch ($provider) {
            case 'together_ai':
                return $this->callTogetherAI($apiKey, $data);

            case 'runware':
                return $this->callRunware($apiKey, $data);

            default:
                // For other providers, use placeholder for now
                sleep(2); // Simulate processing time
                return [
                    'success' => true,
                    'image_url' => 'https://picsum.photos/' . $data['width'] . '/' . $data['height'],
                    'generation_time' => rand(1, 5)
                ];
        }
    }

    private function callTogetherAI($apiKey, $data) {
        $startTime = microtime(true);

        // Prepare the API request
        $url = 'https://api.together.xyz/v1/images/generations';

        $payload = [
            'model' => 'black-forest-labs/FLUX.1-schnell-Free',
            'prompt' => $data['prompt'],
            'n' => 1,
            'width' => (int)$data['width'],
            'height' => (int)$data['height']
        ];

        // Add negative prompt if provided
        if (!empty($data['negative_prompt'])) {
            $payload['negative_prompt'] = $data['negative_prompt'];
        }

        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];

        // Initialize cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        $generationTime = round(microtime(true) - $startTime, 2);

        // Handle cURL errors
        if ($curlError) {
            log_error('Together AI cURL error: ' . $curlError);
            return [
                'success' => false,
                'error' => 'Network error: ' . $curlError,
                'generation_time' => $generationTime
            ];
        }

        // Handle HTTP errors
        if ($httpCode !== 200) {
            log_error('Together AI HTTP error: ' . $httpCode . ' - ' . $response);

            // Check for rate limiting
            if ($httpCode === 429) {
                $responseData = json_decode($response, true);
                if (isset($responseData['error']['type']) && $responseData['error']['type'] === 'model_rate_limit') {
                    return [
                        'success' => false,
                        'error' => 'Rate limit reached for Together AI free model. Please wait a few minutes before trying again, or consider upgrading to a paid plan.',
                        'generation_time' => $generationTime,
                        'rate_limited' => true
                    ];
                }
            }

            return [
                'success' => false,
                'error' => 'API error (HTTP ' . $httpCode . ')',
                'generation_time' => $generationTime
            ];
        }

        // Parse response
        $responseData = json_decode($response, true);

        if (!$responseData) {
            log_error('Together AI invalid JSON response: ' . $response);
            return [
                'success' => false,
                'error' => 'Invalid response from API',
                'generation_time' => $generationTime
            ];
        }

        // Check for API errors
        if (isset($responseData['error'])) {
            log_error('Together AI API error: ' . json_encode($responseData['error']));
            return [
                'success' => false,
                'error' => $responseData['error']['message'] ?? 'Unknown API error',
                'generation_time' => $generationTime
            ];
        }

        // Extract image URL
        if (!isset($responseData['data']) || !is_array($responseData['data']) || empty($responseData['data'])) {
            log_error('Together AI no image data: ' . $response);
            return [
                'success' => false,
                'error' => 'No image data in response',
                'generation_time' => $generationTime
            ];
        }

        $imageData = $responseData['data'][0];
        if (!isset($imageData['url'])) {
            log_error('Together AI no image URL: ' . json_encode($imageData));
            return [
                'success' => false,
                'error' => 'No image URL in response',
                'generation_time' => $generationTime
            ];
        }

        return [
            'success' => true,
            'image_url' => $imageData['url'],
            'generation_time' => $generationTime
        ];
    }

    private function callRunware($apiKey, $data) {
        $startTime = microtime(true);

        // Prepare the API request
        $url = 'https://api.runware.ai/v1';

        $payload = [
            [
                'taskType' => 'imageInference',
                'taskUUID' => $this->generateUUIDv4(),
                'positivePrompt' => $data['prompt'],
                'width' => (int)$data['width'],
                'height' => (int)$data['height'],
                'model' => $data['model'],
                'numberResults' => 1,
                'outputType' => 'URL'
            ]
        ];

        // Add negative prompt if provided
        if (!empty($data['negative_prompt'])) {
            $payload[0]['negativePrompt'] = $data['negative_prompt'];
        }

        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        $generationTime = round(microtime(true) - $startTime, 2);

        // Handle cURL errors
        if ($curlError) {
            log_error('Runware cURL error: ' . $curlError);
            return [
                'success' => false,
                'error' => 'Network error: ' . $curlError,
                'generation_time' => $generationTime
            ];
        }

        // Handle HTTP errors
        if ($httpCode !== 200) {
            log_error('Runware HTTP error: ' . $httpCode . ' - ' . $response);
            return [
                'success' => false,
                'error' => 'Runware API error (HTTP ' . $httpCode . ')',
                'generation_time' => $generationTime
            ];
        }

        // Parse response
        $responseData = json_decode($response, true);

        if (!$responseData) {
            log_error('Runware invalid JSON response: ' . $response);
            return [
                'success' => false,
                'error' => 'Invalid response from Runware API',
                'generation_time' => $generationTime
            ];
        }

        // Check for errors in response
        if (isset($responseData['errors']) && !empty($responseData['errors'])) {
            $errorMessage = $responseData['errors'][0]['message'] ?? 'Unknown Runware API error';
            log_error('Runware API error: ' . $errorMessage);
            return [
                'success' => false,
                'error' => $errorMessage,
                'generation_time' => $generationTime
            ];
        }

        // Extract image URL
        if (!isset($responseData['data']) || !is_array($responseData['data']) || empty($responseData['data'])) {
            log_error('Runware no image data: ' . $response);
            return [
                'success' => false,
                'error' => 'No image data in response',
                'generation_time' => $generationTime
            ];
        }

        $imageData = $responseData['data'][0];
        if (!isset($imageData['imageURL'])) {
            log_error('Runware no image URL: ' . json_encode($imageData));
            return [
                'success' => false,
                'error' => 'No image URL in response',
                'generation_time' => $generationTime
            ];
        }

        return [
            'success' => true,
            'image_url' => $imageData['imageURL'],
            'generation_time' => $generationTime
        ];
    }

    private function testProviderApiKey($provider, $apiKey) {
        // Test API key by making a simple request
        switch ($provider) {
            case 'together_ai':
                return $this->testTogetherAI($apiKey);

            case 'runware':
                return $this->testRunware($apiKey);

            default:
                // For other providers, use simple validation
                if (strlen($apiKey) < 10) {
                    return ['success' => false, 'error' => 'API key too short'];
                }
                return ['success' => true];
        }
    }

    private function testTogetherAI($apiKey) {
        $url = 'https://api.together.xyz/v1/models';

        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            return ['success' => false, 'error' => 'Network error: ' . $curlError];
        }

        if ($httpCode === 401) {
            return ['success' => false, 'error' => 'Invalid API key'];
        }

        if ($httpCode !== 200) {
            return ['success' => false, 'error' => 'API error (HTTP ' . $httpCode . ')'];
        }

        return ['success' => true];
    }

    private function testRunware($apiKey) {
        // Test Runware API key with a simple authentication request
        $url = 'https://api.runware.ai/v1';

        $payload = [
            [
                'taskType' => 'authentication',
                'apiKey' => $apiKey
            ]
        ];

        $headers = [
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            return ['success' => false, 'error' => 'Network error: ' . $curlError];
        }

        if ($httpCode === 401) {
            return ['success' => false, 'error' => 'Invalid API key'];
        }

        if ($httpCode !== 200) {
            return ['success' => false, 'error' => 'API error (HTTP ' . $httpCode . ')'];
        }

        $responseData = json_decode($response, true);

        // Check for authentication errors
        if (isset($responseData['errors']) && !empty($responseData['errors'])) {
            $error = $responseData['errors'][0];
            if ($error['code'] === 'invalidApiKey') {
                return ['success' => false, 'error' => 'Invalid API key'];
            }
            return ['success' => false, 'error' => $error['message'] ?? 'Authentication failed'];
        }

        // Check for successful authentication
        if (isset($responseData['data']) && !empty($responseData['data'])) {
            $authData = $responseData['data'][0];
            if (isset($authData['connectionSessionUUID'])) {
                return ['success' => true];
            }
        }

        return ['success' => false, 'error' => 'Unexpected response format'];
    }

    private function generateUUIDv4() {
        // Generate a proper UUIDv4 for Runware API
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
}
