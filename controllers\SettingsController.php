<?php

class SettingsController extends BaseController {

    public function index() {
        Auth::requireAuth();

        $user = Auth::user();

        $this->view('settings/index', [
            'user' => $user
        ]);
    }

    public function update() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $data = [
            'first_name' => sanitize($_POST['first_name'] ?? ''),
            'last_name' => sanitize($_POST['last_name'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
        ];

        // Validation
        $errors = $this->validate($data, [
            'first_name' => 'required|min:2|max:50',
            'last_name' => 'required|min:2|max:50',
            'email' => 'required|email'
        ]);

        // Check if email is already taken by another user
        if (!empty($data['email'])) {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$data['email'], $userId]);
            if ($stmt->fetchColumn() > 0) {
                $errors['email'][] = 'Email is already taken.';
            }
        }

        if (!empty($errors)) {
            setOld($data);
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    flash('error', $error);
                }
            }
            $this->redirect('/settings');
        }

        try {
            $db = Database::getInstance();

            // Check if email changed (requires verification)
            $currentUser = Auth::user();
            $emailChanged = $currentUser['email'] !== $data['email'];

            if ($emailChanged) {
                // Generate new verification token
                $verificationToken = Auth::generateEmailVerificationToken();

                $stmt = $db->prepare("
                    UPDATE users
                    SET first_name = ?, last_name = ?, email = ?, email_verified = 0, email_verification_token = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([
                    $data['first_name'],
                    $data['last_name'],
                    $data['email'],
                    $verificationToken,
                    $userId
                ]);

                // Send verification email
                Auth::sendVerificationEmail([
                    'id' => $userId,
                    'email' => $data['email'],
                    'first_name' => $data['first_name']
                ]);

                flash('warning', 'Profile updated! Please verify your new email address.');
            } else {
                $stmt = $db->prepare("
                    UPDATE users
                    SET first_name = ?, last_name = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([
                    $data['first_name'],
                    $data['last_name'],
                    $userId
                ]);

                flash('success', 'Profile updated successfully!');
            }

            clearOld();

        } catch (Exception $e) {
            log_error('Profile update failed: ' . $e->getMessage(), $data);
            flash('error', 'Failed to update profile. Please try again.');
            setOld($data);
        }

        $this->redirect('/settings');
    }

    public function apiKeys() {
        Auth::requireAuth();

        $userId = Auth::id();
        $db = Database::getInstance();

        // Get user's API keys
        $stmt = $db->prepare("
            SELECT id, provider, is_active, last_used, created_at
            FROM user_ai_provider_keys
            WHERE user_id = ?
            ORDER BY provider
        ");
        $stmt->execute([$userId]);
        $apiKeys = $stmt->fetchAll();

        // Get supported providers
        $supportedProviders = get_supported_ai_providers();

        $this->view('settings/api-keys', [
            'apiKeys' => $apiKeys,
            'supportedProviders' => $supportedProviders
        ]);
    }

    public function saveApiKey() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $provider = sanitize($_POST['provider'] ?? '');
        $apiKey = $_POST['api_key'] ?? '';

        if (empty($provider) || empty($apiKey)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Provider and API key are required.'], 400);
                return;
            }
            flash('error', 'Provider and API key are required.');
            $this->redirect('/api-keys');
            return;
        }

        // Validate provider
        $supportedProviders = get_supported_ai_providers();
        if (!in_array($provider, $supportedProviders)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Invalid provider selected.'], 400);
                return;
            }
            flash('error', 'Invalid provider selected.');
            $this->redirect('/api-keys');
            return;
        }

        try {
            $db = Database::getInstance();

            // Encrypt API key
            $encryptedKey = encrypt($apiKey);

            // Check if key already exists for this provider
            $stmt = $db->prepare("SELECT id FROM user_ai_provider_keys WHERE user_id = ? AND provider = ?");
            $stmt->execute([$userId, $provider]);
            $existing = $stmt->fetch();

            if ($existing) {
                // Update existing key
                $stmt = $db->prepare("
                    UPDATE user_ai_provider_keys
                    SET api_key = ?, is_active = 1, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$encryptedKey, $existing['id']]);
            } else {
                // Insert new key
                $stmt = $db->prepare("
                    INSERT INTO user_ai_provider_keys (user_id, provider, api_key, is_active)
                    VALUES (?, ?, ?, 1)
                ");
                $stmt->execute([$userId, $provider, $encryptedKey]);
            }

            log_activity($userId, 'api_key_saved', ['provider' => $provider]);

            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => 'API key saved successfully!']);
                return;
            }

            flash('success', 'API key saved successfully!');

        } catch (Exception $e) {
            log_error('API key save failed: ' . $e->getMessage(), ['provider' => $provider]);

            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Failed to save API key. Please try again.'], 500);
                return;
            }

            flash('error', 'Failed to save API key. Please try again.');
        }

        $this->redirect('/api-keys');
    }

    public function deleteApiKey($id) {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();

        try {
            $db = Database::getInstance();

            // Verify ownership
            $stmt = $db->prepare("SELECT provider FROM user_ai_provider_keys WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);
            $apiKey = $stmt->fetch();

            if (!$apiKey) {
                if ($this->isAjaxRequest()) {
                    $this->json(['success' => false, 'message' => 'API key not found.'], 404);
                }
                flash('error', 'API key not found.');
                $this->redirect('/api-keys');
            }

            // Delete the key
            $stmt = $db->prepare("DELETE FROM user_ai_provider_keys WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);

            log_activity($userId, 'api_key_deleted', ['provider' => $apiKey['provider']]);

            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => 'API key deleted successfully!']);
            }

            flash('success', 'API key deleted successfully!');

        } catch (Exception $e) {
            log_error('API key deletion failed: ' . $e->getMessage(), ['id' => $id]);

            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Failed to delete API key. Please try again.'], 500);
            }

            flash('error', 'Failed to delete API key. Please try again.');
        }

        $this->redirect('/api-keys');
    }

    public function changePassword() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $data = [
            'current_password' => $_POST['current_password'] ?? '',
            'new_password' => $_POST['new_password'] ?? '',
            'new_password_confirmation' => $_POST['new_password_confirmation'] ?? ''
        ];

        // Validation
        $errors = $this->validate($data, [
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed'
        ]);

        if (!empty($errors)) {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    flash('error', $error);
                }
            }
            $this->redirect('/settings');
        }

        try {
            $db = Database::getInstance();

            // Verify current password
            $user = Auth::user();
            if (!Auth::verifyPassword($data['current_password'], $user['password_hash'])) {
                flash('error', 'Current password is incorrect.');
                $this->redirect('/settings');
            }

            // Update password
            $newPasswordHash = Auth::hashPassword($data['new_password']);
            $stmt = $db->prepare("UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$newPasswordHash, $userId]);

            flash('success', 'Password changed successfully!');
            log_activity($userId, 'password_changed');

        } catch (Exception $e) {
            log_error('Password change failed: ' . $e->getMessage());
            flash('error', 'Failed to change password. Please try again.');
        }

        $this->redirect('/settings');
    }

    public function deleteAccount() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $password = $_POST['password'] ?? '';

        if (empty($password)) {
            flash('error', 'Password is required to delete account.');
            $this->redirect('/settings');
        }

        try {
            $db = Database::getInstance();

            // Verify password
            $user = Auth::user();
            if (!Auth::verifyPassword($password, $user['password_hash'])) {
                flash('error', 'Password is incorrect.');
                $this->redirect('/settings');
            }

            $db->beginTransaction();

            // Delete user data (cascade will handle related records)
            $stmt = $db->prepare("UPDATE users SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$userId]);

            $db->commit();

            // Log out user
            Auth::logout();

            flash('success', 'Your account has been deleted successfully.');
            $this->redirect('/');

        } catch (Exception $e) {
            $db->rollback();
            log_error('Account deletion failed: ' . $e->getMessage());
            flash('error', 'Failed to delete account. Please try again.');
            $this->redirect('/settings');
        }
    }
}
