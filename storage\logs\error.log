[2025-05-28 14:33:53] ERROR: Together AI HTTP error: 429 - {
  "id": "nvrGeJm-4yUbBN-946dc8754ba0a07f-SEA",
  "error": {
    "message": "You have reached the rate limit specific to this model black-forest-labs/FLUX.1-schnell-<PERSON>. The maximum rate limit for this model is 6.0 queries per minute. This limit differs from the general rate limits published at Together AI rate limits documentation (https://docs.together.ai/docs/rate-limits). For inquiries about increasing your model-specific rate limit, please contact our sales team (https://www.together.ai/forms/contact-sales)",
    "type": "model_rate_limit",
    "param": null,
    "code": null
  }
}
[2025-05-28 14:42:33] ERROR: Together AI HTTP error: 429 - {
  "id": "nvrKJia-4yUbBN-946dd52329d7815b-SEA",
  "error": {
    "message": "You have reached the rate limit specific to this model black-forest-labs/FLUX.1-schnell-<PERSON>. The maximum rate limit for this model is 6.0 queries per minute. This limit differs from the general rate limits published at Together AI rate limits documentation (https://docs.together.ai/docs/rate-limits). For inquiries about increasing your model-specific rate limit, please contact our sales team (https://www.together.ai/forms/contact-sales)",
    "type": "model_rate_limit",
    "param": null,
    "code": null
  }
}
[2025-05-28 14:44:02] ERROR: Runware HTTP error: 400 - {
    "data": [],
    "errors": [
        {
            "code": "invalidTaskUUID",
            "message": "Invalid value for taskUUID parameter. Task UUID must be a valid UUIDv4 string.",
            "parameter": "taskUUID",
            "type": "string",
            "documentation": "https://runware.ai/docs/en/image-inference/api-reference#request-taskuuid",
            "taskUUID": "683705115cbee"
        }
    ]
}
[2025-05-28 14:44:03] ERROR: Runware HTTP error: 400 - {
    "data": [],
    "errors": [
        {
            "code": "invalidTaskUUID",
            "message": "Invalid value for taskUUID parameter. Task UUID must be a valid UUIDv4 string.",
            "parameter": "taskUUID",
            "type": "string",
            "documentation": "https://runware.ai/docs/en/image-inference/api-reference#request-taskuuid",
            "taskUUID": "68370512f14bb"
        }
    ]
}
[2025-05-28 14:54:03] ERROR: Together AI HTTP error: 400 - {
  "id": "nvrNqmq-4yUbBN-946de5f77972a75a-KHI",
  "error": {
    "message": "expected string, received null",
    "type": "invalid_request_error",
    "param": "prompt",
    "code": null
  }
}
[2025-05-28 14:54:03] ERROR: Together AI HTTP error: 429 - {
  "id": "nvrNqzW-4yUbBN-946de5ffc9f8c7aa-SEA",
  "error": {
    "message": "You have reached the rate limit specific to this model black-forest-labs/FLUX.1-schnell-Free. The maximum rate limit for this model is 6.0 queries per minute. This limit differs from the general rate limits published at Together AI rate limits documentation (https://docs.together.ai/docs/rate-limits). For inquiries about increasing your model-specific rate limit, please contact our sales team (https://www.together.ai/forms/contact-sales)",
    "type": "model_rate_limit",
    "param": null,
    "code": null
  }
}
