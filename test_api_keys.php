<?php
/**
 * Test script to verify API key testing functionality
 */

// Include necessary files
require_once 'includes/functions.php';
require_once 'config/app.php';
require_once 'controllers/ApiController.php';

echo "=== API Key Testing Verification ===\n\n";

// Create an instance of ApiController to test methods
$apiController = new ApiController();

// Test 1: Test UUID generation
echo "Testing UUID generation...\n";
$reflection = new ReflectionClass($apiController);
$method = $reflection->getMethod('generateUUIDv4');
$method->setAccessible(true);

try {
    $uuid = $method->invoke($apiController);
    echo "Generated UUID: $uuid\n";
    
    if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid)) {
        echo "✅ UUID format is valid UUIDv4\n";
    } else {
        echo "❌ UUID format is invalid\n";
    }
} catch (Exception $e) {
    echo "❌ UUID generation failed: " . $e->getMessage() . "\n";
}

// Test 2: Test API key validation methods exist
echo "\nTesting API key validation methods...\n";

$methods = ['testProviderApiKey', 'testTogetherAI', 'testRunware'];
foreach ($methods as $methodName) {
    if (method_exists($apiController, $methodName)) {
        echo "✅ Method $methodName exists\n";
    } else {
        echo "❌ Method $methodName missing\n";
    }
}

// Test 3: Test with invalid API keys (should fail gracefully)
echo "\nTesting with invalid API keys...\n";

$testMethod = $reflection->getMethod('testProviderApiKey');
$testMethod->setAccessible(true);

// Test with invalid Together AI key
try {
    $result = $testMethod->invoke($apiController, 'together_ai', 'invalid_key_123');
    if (isset($result['success']) && $result['success'] === false) {
        echo "✅ Together AI invalid key test passed (correctly failed)\n";
    } else {
        echo "❌ Together AI invalid key test failed\n";
    }
} catch (Exception $e) {
    echo "⚠️  Together AI test threw exception: " . $e->getMessage() . "\n";
}

// Test with invalid Runware key
try {
    $result = $testMethod->invoke($apiController, 'runware', 'invalid_key_123');
    if (isset($result['success']) && $result['success'] === false) {
        echo "✅ Runware invalid key test passed (correctly failed)\n";
    } else {
        echo "❌ Runware invalid key test failed\n";
    }
} catch (Exception $e) {
    echo "⚠️  Runware test threw exception: " . $e->getMessage() . "\n";
}

// Test 4: Check if API endpoint exists
echo "\nTesting API endpoint...\n";
if (file_exists('index.php')) {
    $content = file_get_contents('index.php');
    if (strpos($content, "post('/api/test-key'") !== false) {
        echo "✅ API test-key endpoint exists\n";
    } else {
        echo "❌ API test-key endpoint missing\n";
    }
}

echo "\n=== API Key Test Complete ===\n";
echo "Note: To fully test API keys, you need valid API keys from the providers.\n";
echo "The tests above verify that the validation methods exist and handle invalid keys correctly.\n";
?>
