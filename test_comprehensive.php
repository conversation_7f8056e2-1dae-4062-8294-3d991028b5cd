<?php
/**
 * Comprehensive test script to verify all fixes
 */

// Include necessary files
require_once 'includes/functions.php';
require_once 'config/app.php';
require_once 'config/database.php';

echo "=== Comprehensive Integration Test ===\n\n";

// Test 1: Check if Runware is in supported providers
$supportedProviders = Config::get('ai_providers.supported', []);
echo "Supported providers: " . implode(', ', $supportedProviders) . "\n";

if (in_array('runware', $supportedProviders)) {
    echo "✅ Runware is properly configured as a supported provider\n";
} else {
    echo "❌ Runware is NOT configured as a supported provider\n";
}

// Test 2: Check if the controller files exist and have UUID methods
$controllers = [
    'controllers/GenerateController.php',
    'controllers/ApiController.php'
];

foreach ($controllers as $controller) {
    if (file_exists($controller) && is_readable($controller)) {
        $content = file_get_contents($controller);
        if (strpos($content, 'generateUUIDv4') !== false) {
            echo "✅ $controller has UUID generation method\n";
        } else {
            echo "❌ $controller missing UUID generation method\n";
        }
        
        if (strpos($content, 'callRunware') !== false) {
            echo "✅ $controller has Runware integration\n";
        } else {
            echo "❌ $controller missing Runware integration\n";
        }
    } else {
        echo "❌ $controller is missing or not readable\n";
    }
}

// Test 3: Check gallery view fixes
$galleryView = 'views/gallery/index.php';
if (file_exists($galleryView)) {
    $content = file_get_contents($galleryView);
    if (strpos($content, "url('serve/") !== false) {
        echo "✅ Gallery view uses proper image serving URLs\n";
    } else {
        echo "❌ Gallery view still uses broken image URLs\n";
    }
    
    if (strpos($content, "url('gallery/") !== false) {
        echo "✅ Gallery view uses proper navigation URLs\n";
    } else {
        echo "❌ Gallery view has broken navigation URLs\n";
    }
} else {
    echo "❌ Gallery view is missing\n";
}

// Test 4: Check if gallery show view exists
if (file_exists('views/gallery/show.php')) {
    echo "✅ Gallery show view exists\n";
} else {
    echo "❌ Gallery show view is missing\n";
}

// Test 5: Check storage directories
$directories = [
    'storage/generated',
    'storage/logs',
    'storage/uploads'
];

foreach ($directories as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "✅ $dir exists and is writable\n";
    } else {
        echo "❌ $dir is missing or not writable\n";
    }
}

// Test 6: Check database connection and schema
try {
    $db = Database::getInstance();
    echo "✅ Database connection successful\n";
    
    // Check if generated_images table exists
    $stmt = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='generated_images'");
    if ($stmt->fetch()) {
        echo "✅ generated_images table exists\n";
        
        // Check for existing images
        $stmt = $db->query("SELECT COUNT(*) as count FROM generated_images");
        $result = $stmt->fetch();
        echo "📊 Found {$result['count']} images in database\n";
    } else {
        echo "❌ generated_images table missing\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test 7: Check routing configuration
if (file_exists('index.php')) {
    $content = file_get_contents('index.php');
    if (strpos($content, "get('/serve/{id}'") !== false) {
        echo "✅ Image serving route exists\n";
    } else {
        echo "❌ Image serving route missing\n";
    }
} else {
    echo "❌ Main index.php file missing\n";
}

// Test 8: Test UUID generation
echo "\n=== UUID Generation Test ===\n";
function generateUUIDv4() {
    $data = random_bytes(16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

$uuid = generateUUIDv4();
echo "Generated UUID: $uuid\n";
if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid)) {
    echo "✅ UUID format is valid UUIDv4\n";
} else {
    echo "❌ UUID format is invalid\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests show ✅, the application should be working correctly.\n";
?>
