<?php
/**
 * Final verification script for all critical fixes
 */

echo "<!DOCTYPE html>\n<html><head><title>Fix Verification</title>";
echo "<style>body{font-family:Arial;margin:20px;} .pass{color:green;} .fail{color:red;} .info{color:blue;}</style></head><body>";
echo "<h1>🔧 Critical Fixes Verification</h1>";

// Include necessary files
require_once 'includes/functions.php';
require_once 'config/app.php';

$allPassed = true;

echo "<h2>1. Gallery Image Serving Fix</h2>";
$galleryView = 'views/gallery/index.php';
if (file_exists($galleryView)) {
    $content = file_get_contents($galleryView);
    if (strpos($content, "url('serve/") !== false) {
        echo "<p class='pass'>✅ Gallery uses proper image serving URLs</p>";
    } else {
        echo "<p class='fail'>❌ Gallery still uses broken image URLs</p>";
        $allPassed = false;
    }
    
    if (strpos($content, "onerror=") !== false) {
        echo "<p class='pass'>✅ Gallery has error handling for missing images</p>";
    } else {
        echo "<p class='fail'>❌ Gallery missing error handling</p>";
        $allPassed = false;
    }
} else {
    echo "<p class='fail'>❌ Gallery view missing</p>";
    $allPassed = false;
}

echo "<h2>2. Navigation Links Fix</h2>";
if (strpos($content, "url('gallery/") !== false) {
    echo "<p class='pass'>✅ Gallery navigation uses proper URLs</p>";
} else {
    echo "<p class='fail'>❌ Gallery navigation has broken URLs</p>";
    $allPassed = false;
}

echo "<h2>3. Missing Gallery Show View</h2>";
if (file_exists('views/gallery/show.php')) {
    echo "<p class='pass'>✅ Gallery show view created</p>";
} else {
    echo "<p class='fail'>❌ Gallery show view missing</p>";
    $allPassed = false;
}

echo "<h2>4. Runware UUID Fix</h2>";
$controllers = ['controllers/GenerateController.php', 'controllers/ApiController.php'];
foreach ($controllers as $controller) {
    if (file_exists($controller)) {
        $content = file_get_contents($controller);
        if (strpos($content, 'generateUUIDv4') !== false) {
            echo "<p class='pass'>✅ $controller has UUID generation method</p>";
        } else {
            echo "<p class='fail'>❌ $controller missing UUID method</p>";
            $allPassed = false;
        }
        
        if (strpos($content, '$this->generateUUIDv4()') !== false) {
            echo "<p class='pass'>✅ $controller uses proper UUID generation</p>";
        } else {
            echo "<p class='fail'>❌ $controller not using UUID method</p>";
            $allPassed = false;
        }
    }
}

echo "<h2>5. API Key Testing Fix</h2>";
$apiController = 'controllers/ApiController.php';
if (file_exists($apiController)) {
    $content = file_get_contents($apiController);
    if (strpos($content, 'testRunware') !== false) {
        echo "<p class='pass'>✅ Runware API key testing method exists</p>";
    } else {
        echo "<p class='fail'>❌ Runware API key testing missing</p>";
        $allPassed = false;
    }
    
    if (strpos($content, 'testTogetherAI') !== false) {
        echo "<p class='pass'>✅ Together AI key testing method exists</p>";
    } else {
        echo "<p class='fail'>❌ Together AI key testing missing</p>";
        $allPassed = false;
    }
}

echo "<h2>6. Error Handling Improvements</h2>";
foreach ($controllers as $controller) {
    if (file_exists($controller)) {
        $content = file_get_contents($controller);
        if (strpos($content, 'rate_limited') !== false) {
            echo "<p class='pass'>✅ $controller has rate limiting detection</p>";
        } else {
            echo "<p class='fail'>❌ $controller missing rate limiting detection</p>";
            $allPassed = false;
        }
    }
}

echo "<h2>7. Storage and Routing</h2>";
$directories = ['storage/generated', 'storage/logs'];
foreach ($directories as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "<p class='pass'>✅ $dir exists and writable</p>";
    } else {
        echo "<p class='fail'>❌ $dir not accessible</p>";
        $allPassed = false;
    }
}

if (file_exists('index.php')) {
    $content = file_get_contents('index.php');
    if (strpos($content, "get('/serve/{id}'") !== false) {
        echo "<p class='pass'>✅ Image serving route exists</p>";
    } else {
        echo "<p class='fail'>❌ Image serving route missing</p>";
        $allPassed = false;
    }
}

echo "<h2>8. Database Connectivity</h2>";
try {
    require_once 'config/database.php';
    $db = Database::getInstance();
    echo "<p class='pass'>✅ Database connection successful</p>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM generated_images");
    $result = $stmt->fetch();
    echo "<p class='info'>📊 Found {$result['count']} images in database</p>";
} catch (Exception $e) {
    echo "<p class='fail'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $allPassed = false;
}

echo "<h2>9. UUID Generation Test</h2>";
function testUUIDv4() {
    $data = random_bytes(16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

$uuid = testUUIDv4();
if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid)) {
    echo "<p class='pass'>✅ UUID generation works correctly</p>";
    echo "<p class='info'>Sample UUID: $uuid</p>";
} else {
    echo "<p class='fail'>❌ UUID generation broken</p>";
    $allPassed = false;
}

echo "<hr><h2>🎯 Final Result</h2>";
if ($allPassed) {
    echo "<h3 class='pass'>🎉 ALL CRITICAL FIXES VERIFIED SUCCESSFULLY!</h3>";
    echo "<p>Your application should now be fully functional:</p>";
    echo "<ul>";
    echo "<li>✅ Gallery images display correctly</li>";
    echo "<li>✅ Navigation links work properly</li>";
    echo "<li>✅ Image generation with Runware works</li>";
    echo "<li>✅ API key testing functions properly</li>";
    echo "<li>✅ Error handling is improved</li>";
    echo "</ul>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test with real API keys</li>";
    echo "<li>Generate some test images</li>";
    echo "<li>Verify gallery functionality</li>";
    echo "<li>Test the complete workflow</li>";
    echo "</ol>";
} else {
    echo "<h3 class='fail'>⚠️ SOME ISSUES STILL NEED ATTENTION</h3>";
    echo "<p>Please review the failed checks above and ensure all files are properly updated.</p>";
}

echo "<p><a href='/'>🏠 Go to Application</a> | <a href='/gallery'>🖼️ View Gallery</a> | <a href='/generate'>✨ Generate Images</a></p>";
echo "</body></html>";
?>
