<?php
ob_start();
?>

<div class="container py-4">
    <div class="row">
        <div class="col-md-8">
            <!-- Image Display -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <?php if ($image['status'] === 'completed'): ?>
                        <img src="<?= url('serve/' . $image['id']) ?>" 
                             class="img-fluid w-100" 
                             alt="Generated image"
                             style="max-height: 600px; object-fit: contain;">
                    <?php else: ?>
                        <div class="d-flex align-items-center justify-content-center bg-light" style="height: 400px;">
                            <?php if ($image['status'] === 'pending'): ?>
                                <div class="text-center">
                                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Generation Pending</h5>
                                    <p class="text-muted">Your image is in the queue</p>
                                </div>
                            <?php elseif ($image['status'] === 'generating'): ?>
                                <div class="text-center">
                                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;"></div>
                                    <h5 class="text-muted">Generating...</h5>
                                    <p class="text-muted">Please wait while we create your image</p>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                    <h5 class="text-danger">Generation Failed</h5>
                                    <p class="text-muted"><?= htmlspecialchars($image['error_message'] ?? 'Unknown error occurred') ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($image['status'] === 'completed'): ?>
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="<?= url('download/' . $image['id']) ?>" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Download
                            </a>
                            <button type="button" class="btn btn-info" onclick="regenerateImage(<?= $image['id'] ?>)">
                                <i class="fas fa-redo me-2"></i>Regenerate
                            </button>
                        </div>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteImage(<?= $image['id'] ?>)">
                            <i class="fas fa-trash me-2"></i>Delete
                        </button>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Image Details -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Image Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Prompt</label>
                        <p class="text-muted"><?= htmlspecialchars($image['prompt']) ?></p>
                    </div>
                    
                    <?php if (!empty($image['negative_prompt'])): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Negative Prompt</label>
                        <p class="text-muted"><?= htmlspecialchars($image['negative_prompt']) ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Dimensions</label>
                                <p class="text-muted"><?= $image['width'] ?>×<?= $image['height'] ?></p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Provider</label>
                                <p class="text-muted"><?= ucfirst(str_replace('_', ' ', $image['ai_provider'])) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($image['model'])): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Model</label>
                        <p class="text-muted"><?= htmlspecialchars($image['model']) ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <p>
                                    <?php if ($image['status'] === 'completed'): ?>
                                        <span class="badge bg-success">Completed</span>
                                    <?php elseif ($image['status'] === 'generating'): ?>
                                        <span class="badge bg-primary">Generating</span>
                                    <?php elseif ($image['status'] === 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Failed</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Created</label>
                                <p class="text-muted"><?= date('M j, Y g:i A', strtotime($image['created_at'])) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($image['generation_time']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Generation Time</label>
                        <p class="text-muted"><?= $image['generation_time'] ?>s</p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($image['file_size']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">File Size</label>
                        <p class="text-muted"><?= number_format($image['file_size'] / 1024, 1) ?> KB</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Related Images -->
            <?php if (!empty($relatedImages)): ?>
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Related Images</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php foreach ($relatedImages as $related): ?>
                            <div class="col-6">
                                <?php if ($related['status'] === 'completed'): ?>
                                    <img src="<?= url('serve/' . $related['id']) ?>" 
                                         class="img-fluid rounded cursor-pointer" 
                                         alt="Related image"
                                         style="height: 80px; width: 100%; object-fit: cover;"
                                         onclick="viewImage(<?= $related['id'] ?>)">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                        <small class="text-muted"><?= ucfirst($related['status']) ?></small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Back to Gallery -->
    <div class="row mt-4">
        <div class="col">
            <a href="<?= url('gallery') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Gallery
            </a>
        </div>
    </div>
</div>

<script>
function viewImage(id) {
    window.location.href = '<?= url('gallery/') ?>' + id;
}

function deleteImage(id) {
    if (confirm('Are you sure you want to delete this image?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= url('gallery/') ?>' + id + '/delete';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function regenerateImage(id) {
    if (confirm('Are you sure you want to regenerate this image? This will use one of your generations.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= url('gallery/') ?>' + id + '/regenerate';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh for pending/generating images
<?php if ($image['status'] === 'pending' || $image['status'] === 'generating'): ?>
setTimeout(function() {
    location.reload();
}, 5000); // Refresh every 5 seconds
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
$title = 'Image Details';
include __DIR__ . '/../layout/app.php';
?>
